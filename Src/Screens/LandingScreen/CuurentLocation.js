import {
  Alert,
  Dimensions,
  Image,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React from 'react';
import designeSheet from '../../Designe/designeSheet';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import {promptForEnableLocationIfNeeded} from 'react-native-android-location-enabler';
import {useDispatch} from 'react-redux';
import {setbottomvalue, setData} from '../../Redux/CreatSlice';

const CuurentLocation = props => {
  const dispatch = useDispatch();
  async function handleEnabledPressed() {
    if (Platform.OS === 'android') {
      try {
        const enableResult = await promptForEnableLocationIfNeeded();
        Alert.alert(enableResult, '', [
          {
            text: 'OK',
            onPress: () => {
              dispatch(setData('1'));
              dispatch(setbottomvalue(0));
            },
          },
        ]);
      } catch (error) {
        if (error instanceof Error) {
        }
      }
    } else {
      dispatch(setData('1'));
      dispatch(setbottomvalue(0));
    }
  }
  const handleLogin = () => {
    handleEnabledPressed();
  };
  return (
    <ScrollView
      contentContainerStyle={{
        backgroundColor: '#FFF7FB',
        flex: 1,
      }}>
      <View style={{flex: 1}}>
        <Text
          style={{
            fontSize: 16,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
            alignSelf: 'center',
            marginTop: Platform.OS== 'ios'? 100 :30,
          }}>
          {'Explore services  near you'}
        </Text>
      </View>

      <View style={{flex: 1}}>
        <Image
          source={ImagePath.locmehal}
          style={{height: 196, width: Dimensions.get('screen').width}}
        />
      </View>

      <View style={{flex: 1}}>
        <TouchableOpacity
          style={{
            backgroundColor: '#000000',
            padding: 10,
            borderRadius: 8,
            marginTop: 20,
            marginHorizontal: 20,
          }}
          onPress={handleLogin}>
          <Text
            style={{
              color: 'white',
              textAlign: 'center',
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
            }}>
            {'At my current location'}
          </Text>
        </TouchableOpacity>
        <Text
          style={{
            fontSize: 16,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
            alignSelf: 'center',
            marginTop: 20,
          }}>
          {'or'}
        </Text>
        <TouchableOpacity
          style={{
            borderWidth: 1,
            borderColor: '#CCCCCC',
            padding: 10,
            borderRadius: 8,
            marginTop: 20,
            marginHorizontal: 20,
          }}>
          <Text
            style={{
              color: '#000000',
              textAlign: 'center',
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
            }}>
            {'Choose another location'}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

export default CuurentLocation;
