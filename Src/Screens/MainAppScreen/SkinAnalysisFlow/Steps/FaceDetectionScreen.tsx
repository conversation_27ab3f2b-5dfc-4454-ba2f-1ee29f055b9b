import React, {useRef, useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Image,
} from 'react-native';
import {
  Camera,
  useFrameProcessor,
  CameraDevice,
} from 'react-native-vision-camera';
import {useFaceDetector} from 'react-native-vision-camera-face-detector';
import {useCameraPermission} from '../../../../Hooks/useCameraPermission';

import designeSheet from '../../../../Designe/designeSheet';
import Global from '../../../../Globals/Global';

export default function FaceDetectionScreen() {
  const hasPermission = useCameraPermission();
  const cameraRef = useRef(null);
  const [faceLength, setFaceLength] = useState(0);
  const [device, setDevice] = useState<CameraDevice | null>(null);

  useEffect(() => {
    (async () => {
      const devices = await Camera.getAvailableCameraDevices();
      const front = devices.find(d => d.position === 'front');
      setDevice(front || null);
    })();
  }, []);

  const faceDetector = useFaceDetector({
    performanceMode: 'fast',
    landmarkMode: 'none',
    contourMode: 'none',
    classificationMode: 'none',
  });

  useEffect(() => {
    return () => {
      faceDetector.stopListeners();
    };
  }, [faceDetector]);

  // Monitor faceLength state changes
  // Force update counter
  const [updateCounter, setUpdateCounter] = useState(0);

  const frameProcessor = useFrameProcessor(
    frame => {
      'worklet';

      try {
        const faces = faceDetector.detectFaces(frame);
        console.log('Faces detected:', faces.length);

        // Update global
        Global.facesLength = faces.length;
        console.log('✅ Global.facesLength updated to:', Global.facesLength);
      } catch (e) {
        console.log('Face detection error:', e);
      }
    },
    [faceDetector],
  );

  // Force UI updates every second by reading Global and updating state
  useEffect(() => {
    const forceUpdate = setInterval(() => {
      const globalValue = Global.facesLength || 0;

      // Always update state to force re-render
      setFaceLength(globalValue);
      console.log('🔄 Force update:', globalValue, 'counter:', updateCounter);

      setUpdateCounter(prev => prev + 1);

    }, 1000); // Every 1 second

    return () => clearInterval(forceUpdate);
  }, []); // No dependencies to avoid restart

  if (!hasPermission) {
    return (
      <View style={styles.center}>
        <Text>No Camera Permission</Text>
      </View>
    );
  }

  if (!device) {
    return (
      <View style={styles.center}>
        <Text>Loading camera...</Text>
      </View>
    );
  }

  return (
    <View
      style={{
        height: Dimensions.get('screen').width,
        width: Dimensions.get('screen').width,
      }}>
      <Camera
        ref={cameraRef}
        style={{
          height: Dimensions.get('screen').width,
          width: Dimensions.get('screen').width,
        }}
        device={device}
        isActive={true}
        frameProcessor={frameProcessor}
      />

      <TouchableOpacity
        style={{
          flexDirection: 'row',
          height: 40,
          backgroundColor: 'black',
          borderRadius: 10,
          alignItems: 'center',
          justifyContent: 'center',
          margin: 20,
          opacity: faceLength == 0 ? 0.5 : 1,
        }}>
        <Text
          style={{
            fontSize: 15,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: 'white',
          }}>
          Next
        </Text>
        <Image
          style={{
            width: 18,
            height: 10,
            tintColor: 'white',
            transform: [{rotate: '180deg'}],
            marginTop: 2.5,
          }}
          source={require('../../../../Assets/Images/backArrow.png')}
        />
      </TouchableOpacity>

      <Text style={styles.statusText}>
        {faceLength == 0 ? 'No face detected' : 'Face detected'}
      </Text>
      <Text style={styles.debugText}>Face Length State: {faceLength}</Text>
      <Text style={styles.debugText}>
        Global Variable: {Global.facesLength}
      </Text>
      <Text style={styles.debugText}>Update Counter: {updateCounter}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  center: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  faceText: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    color: '#fff',
    padding: 5,
    fontSize: 100,
  },
  statusText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginVertical: 10,
  },
  debugText: {
    fontSize: 14,
    color: '#ccc',
    textAlign: 'center',
    marginVertical: 2,
  },
});
