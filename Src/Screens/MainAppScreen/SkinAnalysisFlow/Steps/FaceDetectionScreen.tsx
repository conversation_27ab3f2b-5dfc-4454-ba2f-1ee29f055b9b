import React, {useRef, useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Image,
} from 'react-native';
import {
  Camera,
  useFrameProcessor,
  CameraDevice,
} from 'react-native-vision-camera';
import {useFaceDetector} from 'react-native-vision-camera-face-detector';
import {useCameraPermission} from '../../../../Hooks/useCameraPermission';
import {runOnJS} from 'react-native-reanimated';
import designeSheet from '../../../../Designe/designeSheet';
import Global from '../../../../Globals/Global';
var facelengthss = 0;

export default function FaceDetectionScreen() {
  const hasPermission = useCameraPermission();
  const cameraRef = useRef(null);
  const [faceLength, setFaceLength] = useState(0);
  const [faces, setFaces] = useState<
    Array<{x: number; y: number; width: number; height: number}>
  >([]);
  const [device, setDevice] = useState<CameraDevice | null>(null);

  useEffect(() => {
    (async () => {
      const devices = await Camera.getAvailableCameraDevices();
      const front = devices.find(d => d.position === 'front');
      setDevice(front || null);
    })();
  }, []);

  const faceDetector = useFaceDetector({
    performanceMode: 'fast',
    landmarkMode: 'none',
    contourMode: 'none',
    classificationMode: 'none',
  });

  useEffect(() => {
    return () => {
      faceDetector.stopListeners();
    };
  }, [faceDetector]);

  const frameProcessor = useFrameProcessor(
    frame => {
      'worklet';
      try {
        console.log('About to call detectFaces');
        const faces = faceDetector.detectFaces(frame);
        console.log('Faces detected:', faces.length);
        facelengthss = faces.length;
        console.log('sdjkcndsknckdsncknds', facelengthss);
        setFaceLength(facelengthss);
        console.log('jasdbcjdsbcjhbdshjcbdshjcbhjdsbcjdsb', faceLength);
        // Use runOnJS to call React state setters from worklet
        runOnJS(setFaceLength)(faces.length);
        runOnJS(() => {
          console.log('fgacelength', faces.length);
        })();

        Global.facesLength = faces.length;
        console.log('Global.facesLength', Global.facesLength);

        if (faces.length > 0) {
          // Create a simple serializable object
          const safeFaces = [];
          for (let i = 0; i < faces.length; i++) {
            const face = faces[i];
            safeFaces.push({
              x: face.bounds.x,
              y: face.bounds.y,
              width: face.bounds.width,
              height: face.bounds.height,
            });
          }

          console.log('About to call runOnJS');
          console.log('runOnJS called successfully', safeFaces);
          setFaces(safeFaces);
          runOnJS(setFaces)(safeFaces);
        }
      } catch (e) {
        console.log('Face detection error:', e);
      }
    },
    [faceDetector],
  );

  if (!hasPermission) {
    return (
      <View style={styles.center}>
        <Text>No Camera Permission</Text>
      </View>
    );
  }

  if (!device) {
    return (
      <View style={styles.center}>
        <Text>Loading camera...</Text>
      </View>
    );
  }

  return (
    <View
      style={{
        height: Dimensions.get('screen').width,
        width: Dimensions.get('screen').width,
      }}>
      <Camera
        ref={cameraRef}
        style={{
          height: Dimensions.get('screen').width,
          width: Dimensions.get('screen').width,
        }}
        device={device}
        isActive={true}
        frameProcessor={frameProcessor}
      />

      <TouchableOpacity
        style={{
          flexDirection: 'row',
          height: 40,
          backgroundColor: 'black',
          borderRadius: 10,
          alignItems: 'center',
          justifyContent: 'center',
          margin: 20,
          opacity: faces.length == 0 ? 0.5 : 1,
        }}>
        <Text
          style={{
            fontSize: 15,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: 'white',
          }}>
          Next
        </Text>
        <Image
          style={{
            width: 18,
            height: 10,
            tintColor: 'white',
            transform: [{rotate: '180deg'}],
            marginTop: 2.5,
          }}
          source={require('../../../../Assets/Images/backArrow.png')}
        />
      </TouchableOpacity>

      {/* {faces.map((face, index) => (
        <Text key={index} style={styles.faceText}>
          Face {index + 1}: x={face.x.toFixed(0)}, y={face.y.toFixed(0)}
        </Text>
      ))} */}
      <Text>{faces.length == 0 ? 'No face detected' : 'Face detected'}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  center: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  faceText: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    color: '#fff',
    padding: 5,
    fontSize: 100,
  },
});
