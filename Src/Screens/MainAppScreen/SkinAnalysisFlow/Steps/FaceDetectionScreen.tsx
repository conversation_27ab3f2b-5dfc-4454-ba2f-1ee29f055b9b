import React, {useRef, useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Image,
} from 'react-native';
import {
  Camera,
  useFrameProcessor,
  CameraDevice,
} from 'react-native-vision-camera';
import {useFaceDetector} from 'react-native-vision-camera-face-detector';
import {useCameraPermission} from '../../../../Hooks/useCameraPermission';

import designeSheet from '../../../../Designe/designeSheet';
import Global from '../../../../Globals/Global';

export default function FaceDetectionScreen() {
  const hasPermission = useCameraPermission();
  const cameraRef = useRef(null);
  const [faceLength, setFaceLength] = useState(0);
  const [device, setDevice] = useState<CameraDevice | null>(null);

  useEffect(() => {
    (async () => {
      const devices = await Camera.getAvailableCameraDevices();
      const front = devices.find(d => d.position === 'front');
      setDevice(front || null);
    })();
  }, []);

  const faceDetector = useFaceDetector({
    performanceMode: 'fast',
    landmarkMode: 'none',
    contourMode: 'none',
    classificationMode: 'none',
  });

  useEffect(() => {
    return () => {
      faceDetector.stopListeners();
    };
  }, [faceDetector]);

  // Monitor faceLength state changes
  // Simple state for face detection
  const [detectedFaces, setDetectedFaces] = useState(0);

  const frameProcessor = useFrameProcessor(
    frame => {
      'worklet';

      try {
        const faces = faceDetector.detectFaces(frame);
        console.log('Faces detected:', faces.length);

        // Simple approach: just log and update global for now
        Global.facesLength = faces.length;
        console.log('✅ Global.facesLength updated to:', Global.facesLength);
      } catch (e) {
        console.log('Face detection error:', e);
      }
    },
    [faceDetector],
  );

  // Simple timer to update UI based on face detection
  useEffect(() => {
    const timer = setInterval(() => {
      // Read from global and update state
      const currentFaces = Global.facesLength || 0;
      if (currentFaces !== detectedFaces) {
        console.log('✅ Updating UI: faces =', currentFaces);
        setDetectedFaces(currentFaces);
        setFaceLength(currentFaces);
      }
    }, 200);

    return () => clearInterval(timer);
  }, [detectedFaces]);

  if (!hasPermission) {
    return (
      <View style={styles.center}>
        <Text>No Camera Permission</Text>
      </View>
    );
  }

  if (!device) {
    return (
      <View style={styles.center}>
        <Text>Loading camera...</Text>
      </View>
    );
  }

  return (
    <View
      style={{
        height: Dimensions.get('screen').width,
        width: Dimensions.get('screen').width,
      }}>
      <Camera
        ref={cameraRef}
        style={{
          height: Dimensions.get('screen').width,
          width: Dimensions.get('screen').width,
        }}
        device={device}
        isActive={true}
        frameProcessor={frameProcessor}
      />

      <TouchableOpacity
        style={{
          flexDirection: 'row',
          height: 40,
          backgroundColor: 'black',
          borderRadius: 10,
          alignItems: 'center',
          justifyContent: 'center',
          margin: 20,
          opacity: faceLength == 0 ? 0.5 : 1,
        }}>
        <Text
          style={{
            fontSize: 15,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: 'white',
          }}>
          Next
        </Text>
        <Image
          style={{
            width: 18,
            height: 10,
            tintColor: 'white',
            transform: [{rotate: '180deg'}],
            marginTop: 2.5,
          }}
          source={require('../../../../Assets/Images/backArrow.png')}
        />
      </TouchableOpacity>

      <Text style={styles.statusText}>
        {faceLength == 0 ? 'No face detected' : 'Face detected'}
      </Text>
      <Text style={styles.debugText}>Face Length State: {faceLength}</Text>
      <Text style={styles.debugText}>
        Global Variable: {Global.facesLength}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  center: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  faceText: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    color: '#fff',
    padding: 5,
    fontSize: 100,
  },
  statusText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginVertical: 10,
  },
  debugText: {
    fontSize: 14,
    color: '#ccc',
    textAlign: 'center',
    marginVertical: 2,
  },
});
