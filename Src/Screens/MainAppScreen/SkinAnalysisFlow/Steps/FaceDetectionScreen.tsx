import React, {useRef, useState, useEffect} from 'react';
import {View, Text, StyleSheet, Dimensions} from 'react-native';
import {
  Camera,
  useFrameProcessor,
  CameraDevice,
} from 'react-native-vision-camera';
import {useFaceDetector} from 'react-native-vision-camera-face-detector';
import {useCameraPermission} from '../../../../Hooks/useCameraPermission';
import {runOnJS} from 'react-native-reanimated';

export default function FaceDetectionScreen() {
  const hasPermission = useCameraPermission();
  const cameraRef = useRef(null);
  const [faces, setFaces] = useState<
    Array<{x: number; y: number; width: number; height: number}>
  >([]);
  const [device, setDevice] = useState<CameraDevice | null>(null);

  console.log('kjsdbckjdbskjc', runOnJS);
  useEffect(() => {
    (async () => {
      const devices = await Camera.getAvailableCameraDevices();
      const front = devices.find(d => d.position === 'front');
      setDevice(front || null);
    })();
  }, []);

  const faceDetector = useFaceDetector({});

  const frameProcessor = useFrameProcessor(
    frame => {
      'worklet';
      try {
        const detected = faceDetector.detectFaces(frame);
        const safeFaces = detected.map(face => ({
          x: face.bounds.x,
          y: face.bounds.y,
          width: face.bounds.width,
          height: face.bounds.height,
        }));
        runOnJS(setFaces)(safeFaces);
      } catch (e) {
        runOnJS(console.error)('Face detection error:', e);
      }
    },
    [faceDetector],
  );

  if (!hasPermission) {
    return (
      <View style={styles.center}>
        <Text>No Camera Permission</Text>
      </View>
    );
  }

  if (!device) {
    return (
      <View style={styles.center}>
        <Text>Loading camera...</Text>
      </View>
    );
  }

  return (
    <View style={{}}>
      <Camera
        ref={cameraRef}
        style={{
          height: Dimensions.get('screen').width,
          width: Dimensions.get('screen').width,
        }}
        device={device}
        isActive={true}
        frameProcessor={frameProcessor}
      />
      {faces.map((face, index) => (
        <Text key={index} style={styles.faceText}>
          Face {index + 1}: x={face.x.toFixed(0)}, y={face.y.toFixed(0)}
        </Text>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  center: {flex: 1, alignItems: 'center', justifyContent: 'center'},
  faceText: {backgroundColor: 'rgba(0,0,0,0.5)', color: '#fff', padding: 5},
});
