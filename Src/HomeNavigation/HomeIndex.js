import React from 'react';
import BottomTab from '../Screens/MainAppScreen/BottomTab';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import Home from '../Screens/MainAppScreen/Home/Home';
import MyBooking from '../Screens/MainAppScreen/MyBooking';
import Refer from '../Screens/MainAppScreen/Refer';
import Profile from '../Screens/MainAppScreen/Profile';
import BookingDetail from '../Screens/MainAppScreen/BookingDetail';
import ReviewScreen from '../Screens/MainAppScreen/ReviewScreen';
import Chat from '../Screens/MainAppScreen/Chat';
import EditProfile from '../Screens/MainAppScreen/EditProfile';
import SingleServiceScreen from '../Screens/MainAppScreen/CategoryDetails/SingleServiceScreen';
import AddPackage from '../Screens/MainAppScreen/AddPackage';
import ChooseShop from '../Screens/MainAppScreen/Shop/ChooseShop';
import Shop from '../Screens/MainAppScreen/Shop';
import ShopBeauty from '../Screens/MainAppScreen/Shop/NestedScreens/ShopBeauty';
import ViewAll from '../Screens/MainAppScreen/ViewAll';
import ShopSingleProduct from '../Screens/MainAppScreen/ShopSingleProduct';
import Cart from '../Screens/MainAppScreen/Cart';
import WellNess from '../Screens/MainAppScreen/WellNess';
import BookConsult from '../Screens/MainAppScreen/BookConsult';
import Wallet from '../Screens/MainAppScreen/Wallet';
import MyOrder from '../Screens/MainAppScreen/MyOrder';
import MyOrderDetail from '../Screens/MainAppScreen/MyOrderDetail';
import MyAddress from '../Screens/MainAppScreen/MyAddress';
import Notifications from '../Screens/MainAppScreen/Notifications';
import ContactUs from '../Screens/MainAppScreen/ContactUs';
import PrivacyPolicy from '../Screens/MainAppScreen/PrivacyPolicy';
import HelpSupport from '../Screens/MainAppScreen/HelpSupport';
import ChatSupport from '../Screens/MainAppScreen/ChatSupport';
import AboutUs from '../Screens/MainAppScreen/AboutUs';
import Register from '../Screens/MainAppScreen/Register';
import AddCash from '../Screens/MainAppScreen/AddCash';
import Withdraw from '../Screens/MainAppScreen/Withdraw';
import TrasactionHistory from '../Screens/MainAppScreen/TrasactionHistory';
import AllProducts from '../Screens/MainAppScreen/Shop/NestedScreens/AllProducts';
import NewScreen from '../Screens/MainAppScreen/Shop/NestedScreens/NewScreen';
import EcommercCart from '../Screens/MainAppScreen/Carts/EcommercCart';
import AddAddress from '../Screens/MainAppScreen/Carts/AddAddress/AddAddress';
import BeautyCart from '../Screens/MainAppScreen/Carts/BeautyCart';
import UnavailableService from '../Screens/MainAppScreen/UnavailableService';
import ChooseCart from '../Screens/MainAppScreen/Carts/ChooseCart/ChooseCart';
import SkinAnalysis from '../Screens/MainAppScreen/SkinAnalysisFlow/SkinAnalysis';
import CallPickupView from '../Screens/MainAppScreen/Call/CallPickUpView';

const Stack = createNativeStackNavigator();
const HomeIndex = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="BottomTab">
        <Stack.Screen
          name="BottomTab"
          component={BottomTab}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="Home"
          component={Home}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="MyBooking"
          component={MyBooking}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="Refer"
          component={Refer}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="Profile"
          component={Profile}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="BookingDetail"
          component={BookingDetail}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="ReviewScreen"
          component={ReviewScreen}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="Chat"
          component={Chat}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="EditProfile"
          component={EditProfile}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="SingleServiceScreen"
          component={SingleServiceScreen}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="AddPackage"
          component={AddPackage}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="ChooseShop"
          component={ChooseShop}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="Shop"
          component={Shop}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="ShopBeauty"
          component={ShopBeauty}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="ViewAll"
          component={ViewAll}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="ShopSingleProduct"
          component={ShopSingleProduct}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="Cart"
          component={Cart}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="WellNess"
          component={WellNess}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="BookConsult"
          component={BookConsult}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="Wallet"
          component={Wallet}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="MyOrder"
          component={MyOrder}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="MyOrderDetail"
          component={MyOrderDetail}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="MyAddress"
          component={MyAddress}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="Notifications"
          component={Notifications}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="ContactUs"
          component={ContactUs}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="PrivacyPolicy"
          component={PrivacyPolicy}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="HelpSupport"
          component={HelpSupport}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="ChatSupport"
          component={ChatSupport}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="AboutUs"
          component={AboutUs}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="Register"
          component={Register}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="AddCash"
          component={AddCash}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="Withdraw"
          component={Withdraw}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="TrasactionHistory"
          component={TrasactionHistory}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="AllProducts"
          component={AllProducts}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="NewScreen"
          component={NewScreen}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="EcommercCart"
          component={EcommercCart}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="BeautyCart"
          component={BeautyCart}
          options={{headerShown: false}}
        />

        <Stack.Screen
          name="AddAddress"
          component={AddAddress}
          options={{headerShown: false}}
        />

        <Stack.Screen
          name="UnavailableService"
          component={UnavailableService}
          options={{headerShown: false}}
        />

        <Stack.Screen
          name="ChooseCart"
          component={ChooseCart}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="skinAnalysis"
          component={SkinAnalysis}
          options={{headerShown: false}}
        />

        <Stack.Screen
          name="CallPickupView"
          component={CallPickupView}
          options={{headerShown: false}}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default HomeIndex;
